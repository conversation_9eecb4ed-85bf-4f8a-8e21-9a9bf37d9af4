package com.chinamobile.sparrow.springboot.web.controller.media;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.util.FileCopyUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLConnection;
import java.net.URLEncoder;

@Controller
public class DefaultRecordController extends RecordController {

    public DefaultRecordController(AbstractMediaRepository mediaRepository, LoginUtil loginUtil) {
        super(mediaRepository, loginUtil);
    }

    @Override
    public void writeToResponse(HttpServletResponse response, String id, boolean asAttachment) throws Exception {
        Result<Media> _item = mediaRepository.get(id, null);
        if (!_item.isOK()) {
            throw new Exception(_item.message);
        }

        response.reset();

        if (asAttachment) {
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.addHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLEncoder.encode(_item.data.getName()));
        } else {
            String _mediaType = URLConnection.getFileNameMap().getContentTypeFor(_item.data.getName());
            response.setContentType(_mediaType);
        }

        Result<InputStream> _input = mediaRepository.getInputStream(_item.data);
        if (_input.isOK()) {
            FileCopyUtils.copy(_input.data, response.getOutputStream());
        } else {
            throw new Exception(_input.message);
        }
    }

}