package com.chinamobile.sparrow.web;

import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.springboot.web.controller.media.DefaultReaderController;
import com.chinamobile.sparrow.springboot.web.controller.media.DefaultRecordController;
import com.chinamobile.sparrow.springboot.web.controller.media.ReaderController;
import com.chinamobile.sparrow.springboot.web.controller.media.RecordController;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class BeanConfiguration {

    @Bean
    public ReaderController readerController(AbstractMediaRepository mediaRepository, RecordController recordController, LoginUtil loginUtil) {
        return new DefaultReaderController(mediaRepository, recordController, loginUtil);
    }

    @Bean
    public RecordController recordController(AbstractMediaRepository mediaRepository, LoginUtil loginUtil) {
        return new DefaultRecordController(mediaRepository, loginUtil);
    }

}